#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
叶坪景区接待工作记录单填写脚本
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
import random
import calendar
import os
from datetime import datetime, timedelta
import csv

class ExcelFiller:
    def __init__(self):
        self.base_path = "."
        self.csv_path = "2019"
        self.years = list(range(2015, 2025))  # 2015-2024年
        
        # 休息日设置：每周五、六休息
        self.rest_days = [4, 5]  # 0=周一, 4=周五, 5=周六
        
        # 产假时间设置
        self.maternity_leave = {
            2018: [(1, 7)],  # 2018年1-7月
            2022: [(6, 12)]  # 2022年6-12月
        }
        
        # 职务后缀 - 分为个人职务和团体
        self.university_individual_positions = [
            "校长", "副校长", "党委书记", "党委副书记", "教务处长",
            "学生处长", "团委书记", "院长", "副院长", "系主任",
            "系教授", "系副教授", "系讲师", "处长", "副处长"
        ]

        self.university_group_suffixes = [
            "系大一学生", "系大二学生", "系大三学生", "系大四学生",
            "系研究生", "系博士生", "师生代表团", "学生会", "社团",
            "实习团", "参观团", "学习团", "交流团"
        ]
        
        self.company_suffixes = [
            "团队", "考察团", "参观团", "学习团", "交流团",
            "代表团", "访问团", "调研团", "培训团", "观摩团",
            "公司领导", "部门经理", "项目组", "工作组", "考察组"
        ]
        
    def load_csv_data(self):
        """加载CSV数据"""
        csv_files = {
            '高等学校.csv': 'university',
            '北京.csv': 'company',
            '上海.csv': 'company',
            '广东.csv': 'company',
            '江苏.csv': 'company',
            '浙江.csv': 'company'
        }
        
        data = {'university': [], 'company': []}
        
        for filename, data_type in csv_files.items():
            filepath = os.path.join(self.csv_path, filename)
            if os.path.exists(filepath):
                try:
                    # 尝试不同的编码
                    for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                        try:
                            df = pd.read_csv(filepath, encoding=encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                    
                    if data_type == 'university':
                        # 高等学校CSV，读取第二列（学校名）
                        if len(df.columns) >= 2:
                            schools = df.iloc[1:, 1].dropna().tolist()  # 从第二行开始，第二列
                            data['university'].extend(schools)
                    else:
                        # 其他CSV，读取第一列（企业名称）
                        if len(df.columns) >= 1:
                            companies = df.iloc[1:, 0].dropna().tolist()  # 从第二行开始，第一列
                            data['company'].extend(companies)
                            
                except Exception as e:
                    print(f"读取文件 {filename} 时出错: {e}")
        
        return data
        
    def is_working_day(self, date):
        """判断是否为工作日"""
        # 检查是否为休息日（周五、周六）
        if date.weekday() in self.rest_days:
            return False
        
        # 检查是否在产假期间
        year = date.year
        month = date.month
        if year in self.maternity_leave:
            for start_month, end_month in self.maternity_leave[year]:
                if start_month <= month <= end_month:
                    return False
        
        return True
        
    def generate_visitor_name_and_count(self, data, visitor_type='mixed'):
        """生成来宾单位名称和对应的人数"""
        if visitor_type == 'university' and data['university']:
            base_name = random.choice(data['university'])
            # 随机选择个人职务或团体
            if random.random() < 0.4:  # 40%概率是个人职务
                suffix = random.choice(self.university_individual_positions)
                count = 1  # 个人职务只有1人
            else:
                suffix = random.choice(self.university_group_suffixes)
                count = self.generate_group_visitor_count()  # 团体多人
            return f"{base_name}{suffix}", count

        elif visitor_type == 'company' and data['company']:
            base_name = random.choice(data['company'])
            suffix = random.choice(self.company_suffixes)
            count = self.generate_group_visitor_count()
            return f"{base_name}{suffix}", count

        else:
            # 混合模式，随机选择
            if data['university'] and data['company']:
                if random.random() < 0.3:  # 30%概率选择高等学校
                    base_name = random.choice(data['university'])
                    if random.random() < 0.4:  # 40%概率是个人职务
                        suffix = random.choice(self.university_individual_positions)
                        count = 1
                    else:
                        suffix = random.choice(self.university_group_suffixes)
                        count = self.generate_group_visitor_count()
                    return f"{base_name}{suffix}", count
                else:
                    base_name = random.choice(data['company'])
                    suffix = random.choice(self.company_suffixes)
                    count = self.generate_group_visitor_count()
                    return f"{base_name}{suffix}", count
            elif data['university']:
                base_name = random.choice(data['university'])
                if random.random() < 0.4:
                    suffix = random.choice(self.university_individual_positions)
                    count = 1
                else:
                    suffix = random.choice(self.university_group_suffixes)
                    count = self.generate_group_visitor_count()
                return f"{base_name}{suffix}", count
            elif data['company']:
                base_name = random.choice(data['company'])
                suffix = random.choice(self.company_suffixes)
                count = self.generate_group_visitor_count()
                return f"{base_name}{suffix}", count

        return "参观团队", self.generate_group_visitor_count()
        
    def generate_group_visitor_count(self):
        """生成团体参观人数"""
        # 大部分是小团队，少数是大团队
        if random.random() < 0.7:
            return random.randint(3, 25)
        else:
            return random.randint(26, 80)
            
    def create_excel_for_year(self, year, data):
        """为指定年份创建Excel文件"""
        filename = f"{year}_fixed.xlsx"
        filepath = os.path.join(self.base_path, filename)

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = f"{year}年接待记录"

        # 设置列宽
        ws.column_dimensions['A'].width = 12  # 时间
        ws.column_dimensions['B'].width = 40  # 来宾单位
        ws.column_dimensions['C'].width = 12  # 参观人数
        ws.column_dimensions['D'].width = 12  # 陪同人员
        ws.column_dimensions['E'].width = 15  # 备注

        current_row = 1
        total_teams = 0
        total_visitors = 0
        used_names = set()  # 记录已使用的团队名称，确保不重复

        # 计算该年度需要工作的月份
        working_months = []
        for month in range(1, 13):
            skip_month = False
            if year in self.maternity_leave:
                for start_month, end_month in self.maternity_leave[year]:
                    if start_month <= month <= end_month:
                        skip_month = True
                        break
            if not skip_month:
                working_months.append(month)

        # 根据年份设置目标团队数量
        if year in [2018, 2022]:  # 产假年份
            min_teams_per_year = 300  # 产假年份少一些
        else:
            min_teams_per_year = 600  # 正常年份多一些

        base_teams_per_month = max(50, min_teams_per_year // len(working_months)) if working_months else 50

        # 为每个月生成数据，每月团队数量有随机性
        for month in working_months:
            # 每月团队数量在基础值的80%-120%之间随机
            month_target = int(base_teams_per_month * random.uniform(0.8, 1.2))
            if year in [2018, 2022]:
                month_target = max(40, min(80, month_target))  # 产假年份40-80之间
            else:
                month_target = max(60, min(100, month_target))  # 正常年份60-100之间

            current_row, month_teams, month_visitors = self.fill_month_data(
                ws, year, month, current_row, data, used_names, month_target
            )
            total_teams += month_teams
            total_visitors += month_visitors

        # 保存文件
        wb.save(filepath)
        print(f"已生成 {filename}，共 {total_teams} 个团队，{total_visitors} 人次")
        
    def fill_month_data(self, ws, year, month, start_row, data, used_names, target_teams):
        """填写某个月的数据"""
        # 月份标题
        title = f"叶坪景区接待工作记录单（邓张姗）"
        ws.merge_cells(f'A{start_row}:E{start_row}')
        ws[f'A{start_row}'] = title
        ws[f'A{start_row}'].alignment = Alignment(horizontal='center', vertical='center')
        ws[f'A{start_row}'].font = Font(bold=True, size=14)

        # 表头
        headers = ['时间', '来宾单位（姓名）', '参观人数', '陪同人员', '备注']
        header_row = start_row + 1
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=header_row, column=col, value=header)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.font = Font(bold=True)

        current_row = header_row + 1
        month_teams = 0
        month_visitors = 0

        # 获取该月的工作日
        working_days = []
        days_in_month = calendar.monthrange(year, month)[1]
        for day in range(1, days_in_month + 1):
            date = datetime(year, month, day)
            if self.is_working_day(date):
                working_days.append(day)

        # 生成该月的接待记录，按日期顺序
        visit_records = []
        teams_generated = 0

        # 为每个工作日随机分配团队
        for day in working_days:
            if teams_generated >= target_teams:
                break

            # 每天接待团队数量：增加频率，让数据更多
            daily_teams = random.choices([1, 2, 3, 4, 5], weights=[20, 30, 25, 15, 10])[0]
            daily_teams = min(daily_teams, target_teams - teams_generated)

            for _ in range(daily_teams):
                if teams_generated >= target_teams:
                    break

                # 生成唯一的团队名称和人数
                attempts = 0
                while attempts < 10:
                    visitor_name, visitor_count = self.generate_visitor_name_and_count(data)
                    if visitor_name not in used_names:
                        used_names.add(visitor_name)
                        break
                    attempts += 1
                else:
                    # 如果10次都重复，添加序号确保唯一性
                    base_name, visitor_count = self.generate_visitor_name_and_count(data)
                    counter = 1
                    while f"{base_name}({counter})" in used_names:
                        counter += 1
                    visitor_name = f"{base_name}({counter})"
                    used_names.add(visitor_name)

                visit_records.append((day, visitor_name, visitor_count))
                teams_generated += 1

        # 按日期排序并写入Excel
        visit_records.sort(key=lambda x: x[0])

        for day, visitor_name, visitor_count in visit_records:
            ws[f'A{current_row}'] = f"{month}月{day}日"
            ws[f'B{current_row}'] = visitor_name
            ws[f'C{current_row}'] = visitor_count
            ws[f'D{current_row}'] = ""  # 陪同人员留空
            ws[f'E{current_row}'] = ""  # 备注留空

            month_teams += 1
            month_visitors += visitor_count
            current_row += 1

        # 月度合计
        ws[f'A{current_row}'] = "合计"
        ws[f'B{current_row}'] = f"{month_teams}批"
        ws[f'C{current_row}'] = month_visitors
        ws[f'D{current_row}'] = ""
        ws[f'E{current_row}'] = ""

        # 设置合计行样式
        for col in range(1, 6):
            cell = ws.cell(row=current_row, column=col)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center', vertical='center')

        return current_row + 2, month_teams, month_visitors  # 返回下一行位置和统计数据
        
    def run(self):
        """运行主程序"""
        print("开始填写Excel表格...")
        
        # 加载CSV数据
        print("正在加载CSV数据...")
        data = self.load_csv_data()
        print(f"加载了 {len(data['university'])} 所高等学校")
        print(f"加载了 {len(data['company'])} 家企业")
        
        # 为每年生成Excel文件
        for year in self.years:
            print(f"正在生成 {year} 年数据...")
            self.create_excel_for_year(year, data)
            
        print("所有Excel文件生成完成！")

if __name__ == "__main__":
    filler = ExcelFiller()
    filler.run()
